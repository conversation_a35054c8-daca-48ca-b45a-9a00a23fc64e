package com.illumio.data.service;

import com.illumio.data.model.ReportSummaryEntity;
import com.illumio.data.model.ReportTagEntity;
import com.illumio.data.repository.iva.ReportSummaryRepository;
import com.illumio.data.repository.iva.ReportTagRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InsightSummaryServiceTest {

    private InsightSummaryService insightSummaryService;

    @Mock
    private ReportSummaryRepository reportSummaryRepository;

    @Mock
    private ReportTagRepository reportTagRepository;

    private static final String TENANT_ID = "550e8400-e29b-41d4-a716-************";
    private static final UUID TENANT_UUID = UUID.fromString(TENANT_ID);
    private static final String PERSONA = "threat_hunter";

    @BeforeEach
    void setUp() {
        insightSummaryService = new InsightSummaryService(reportSummaryRepository, reportTagRepository);
    }

    @Test
    void testGetInsightSummary_ValidDailyRequest_ReturnsSuccess() {
        // Arrange
        ReportSummaryEntity reportEntity = createMockReportEntity();
        List<ReportTagEntity> tagEntities = createMockTagEntities();
        
        when(reportSummaryRepository.findMostRecentDailyReportByTenantAndPersona(eq(TENANT_UUID), eq(PERSONA)))
                .thenReturn(Mono.just(reportEntity));
        when(reportTagRepository.findTagsByReportId(eq(1)))
                .thenReturn(Flux.fromIterable(tagEntities));

        // Act & Assert
        StepVerifier.create(insightSummaryService.getInsightSummary(TENANT_ID, PERSONA, "daily"))
                .assertNext(response -> {
                    assertNotNull(response);
                    assertEquals("1", response.getReportId()); // Integer converted to String
                    assertEquals(PERSONA, response.getPersona());
                    assertEquals(TENANT_ID, response.getIllumioTenantId());
                    assertEquals(2, response.getTags().size());
                    assertEquals("daily", response.getTags().get(0));
                    assertEquals("automatic", response.getTags().get(1));
                })
                .verifyComplete();
    }

    @Test
    void testGetInsightSummary_WeeklyRequest_ReturnsSuccess() {
        // Arrange
        ReportSummaryEntity reportEntity = createMockWeeklyReportEntity();
        List<ReportTagEntity> tagEntities = createMockTagEntities();
        
        when(reportSummaryRepository.findMostRecentWeeklyReportByTenantAndPersona(eq(TENANT_UUID), eq(PERSONA)))
                .thenReturn(Mono.just(reportEntity));
        when(reportTagRepository.findTagsByReportId(eq(1)))
                .thenReturn(Flux.fromIterable(tagEntities));

        // Act & Assert
        StepVerifier.create(insightSummaryService.getInsightSummary(TENANT_ID, PERSONA, "weekly"))
                .assertNext(response -> {
                    assertNotNull(response);
                    assertEquals("1", response.getReportId());
                    assertEquals(PERSONA, response.getPersona());
                    assertEquals(TENANT_ID, response.getIllumioTenantId());
                })
                .verifyComplete();
    }

    @Test
    void testGetInsightSummary_InvalidTenantIdFormat_ThrowsIllegalArgumentException() {
        // Act & Assert
        StepVerifier.create(insightSummaryService.getInsightSummary("invalid-uuid", PERSONA, "daily"))
                .expectError(IllegalArgumentException.class)
                .verify();
    }

    @Test
    void testGetInsightSummary_NoReportFound_ReturnsEmpty() {
        // Arrange
        when(reportSummaryRepository.findMostRecentDailyReportByTenantAndPersona(eq(TENANT_UUID), eq(PERSONA)))
                .thenReturn(Mono.empty());

        // Act & Assert
        StepVerifier.create(insightSummaryService.getInsightSummary(TENANT_ID, PERSONA, "daily"))
                .verifyComplete();
    }

    @Test
    void testGetInsightSummary_RepositoryException_PropagatesError() {
        // Arrange
        when(reportSummaryRepository.findMostRecentDailyReportByTenantAndPersona(eq(TENANT_UUID), eq(PERSONA)))
                .thenReturn(Mono.error(new RuntimeException("Database connection failed")));

        // Act & Assert
        StepVerifier.create(insightSummaryService.getInsightSummary(TENANT_ID, PERSONA, "daily"))
                .expectError(RuntimeException.class)
                .verify();
    }

    @Test
    void testGetInsightSummary_ValidRequestWithEmptyTags_ReturnsResponseWithEmptyTags() {
        // Arrange
        ReportSummaryEntity reportEntity = createMockReportEntity();
        
        when(reportSummaryRepository.findMostRecentDailyReportByTenantAndPersona(eq(TENANT_UUID), eq(PERSONA)))
                .thenReturn(Mono.just(reportEntity));
        when(reportTagRepository.findTagsByReportId(eq(1)))
                .thenReturn(Flux.empty());

        // Act & Assert
        StepVerifier.create(insightSummaryService.getInsightSummary(TENANT_ID, PERSONA, "daily"))
                .assertNext(response -> {
                    assertNotNull(response);
                    assertEquals("1", response.getReportId());
                    assertEquals(0, response.getTags().size());
                })
                .verifyComplete();
    }

    @Test
    void testGetInsightSummary_TagRepositoryException_ReturnsResponseWithoutTags() {
        // Arrange
        ReportSummaryEntity reportEntity = createMockReportEntity();
        
        when(reportSummaryRepository.findMostRecentDailyReportByTenantAndPersona(eq(TENANT_UUID), eq(PERSONA)))
                .thenReturn(Mono.just(reportEntity));
        when(reportTagRepository.findTagsByReportId(eq(1)))
                .thenReturn(Flux.error(new RuntimeException("Tags query failed")));

        // Act & Assert - Service gracefully handles tag errors by returning response without tags
        StepVerifier.create(insightSummaryService.getInsightSummary(TENANT_ID, PERSONA, "daily"))
                .assertNext(response -> {
                    assertNotNull(response);
                    assertEquals("1", response.getReportId());
                    assertEquals(0, response.getTags().size()); // Empty tags due to error
                })
                .verifyComplete();
    }

    @Test
    void testGetInsightSummary_DefaultPersona_ReturnsSuccess() {
        // Arrange
        ReportSummaryEntity reportEntity = createMockReportEntity();
        List<ReportTagEntity> tagEntities = createMockTagEntities();
        
        when(reportSummaryRepository.findMostRecentDailyReportByTenantAndPersona(eq(TENANT_UUID), eq("threat_hunter")))
                .thenReturn(Mono.just(reportEntity));
        when(reportTagRepository.findTagsByReportId(eq(1)))
                .thenReturn(Flux.fromIterable(tagEntities));

        // Act & Assert - test with default persona (should be threat_hunter)
        StepVerifier.create(insightSummaryService.getInsightSummary(TENANT_ID, "threat_hunter", "daily"))
                .assertNext(response -> {
                    assertNotNull(response);
                    assertEquals("threat_hunter", response.getPersona());
                })
                .verifyComplete();
    }

    private ReportSummaryEntity createMockReportEntity() {
        ReportSummaryEntity entity = new ReportSummaryEntity();
        entity.setReportId(1); // Integer ID
        entity.setTenantId(TENANT_UUID);
        entity.setPersona(PERSONA);
        entity.setCurrentStartTime(LocalDateTime.now().minusDays(1));
        entity.setCurrentEndTime(LocalDateTime.now());
        entity.setComparisonStartTime(LocalDateTime.now().minusDays(8));
        entity.setComparisonEndTime(LocalDateTime.now().minusDays(7));
        entity.setCreatedAt(LocalDateTime.now());
        entity.setReportPayload("{\"summary\": \"test data\", \"data_sources\": [\"kusto\", \"logs\"]}");
        return entity;
    }

    private ReportSummaryEntity createMockWeeklyReportEntity() {
        ReportSummaryEntity entity = new ReportSummaryEntity();
        entity.setReportId(1); // Integer ID
        entity.setTenantId(TENANT_UUID);
        entity.setPersona(PERSONA);
        entity.setCurrentStartTime(LocalDateTime.now().minusDays(7));
        entity.setCurrentEndTime(LocalDateTime.now());
        entity.setComparisonStartTime(LocalDateTime.now().minusDays(21));
        entity.setComparisonEndTime(LocalDateTime.now().minusDays(14));
        entity.setCreatedAt(LocalDateTime.now());
        entity.setReportPayload("{\"summary\": \"weekly test data\", \"data_sources\": [\"kusto\", \"logs\"]}");
        return entity;
    }

    private List<ReportTagEntity> createMockTagEntities() {
        ReportTagEntity tag1 = new ReportTagEntity();
        tag1.setTagId(UUID.randomUUID());
        tag1.setDisplayName("daily");
        tag1.setTenantId(TENANT_UUID);

        ReportTagEntity tag2 = new ReportTagEntity();
        tag2.setTagId(UUID.randomUUID());
        tag2.setDisplayName("automatic");
        tag2.setTenantId(TENANT_UUID);

        return List.of(tag1, tag2);
    }
}
